# generated from catkin/cmake/template/order_packages.context.py.in
source_root_dir = '/home/<USER>/ws_voxel_slam2_vscode/src'
whitelisted_packages = ''.split(';') if '' != '' else []
blacklisted_packages = ''.split(';') if '' != '' else []
underlay_workspaces = '/home/<USER>/ws_voxel_slam2_vscode/devel;/home/<USER>/ws_voxel_slam2_vscode/ws_livox/devel;/opt/ros/noetic'.split(';') if '/home/<USER>/ws_voxel_slam2_vscode/devel;/home/<USER>/ws_voxel_slam2_vscode/ws_livox/devel;/opt/ros/noetic' != '' else []
