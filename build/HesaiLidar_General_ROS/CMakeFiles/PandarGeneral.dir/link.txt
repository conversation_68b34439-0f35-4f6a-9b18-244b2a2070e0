/usr/bin/c++ -fPIC   -O3 -DNDEBUG   -shared -Wl,-soname,libPandarGeneral.so -o /home/<USER>/ws_voxel_slam2_vscode/devel/lib/libPandarGeneral.so CMakeFiles/PandarGeneral.dir/src/HesaiLidar_General_SDK/src/PandarGeneralRaw/src/input.cc.o CMakeFiles/PandarGeneral.dir/src/HesaiLidar_General_SDK/src/PandarGeneralRaw/src/pandarGeneral_internal.cc.o CMakeFiles/PandarGeneral.dir/src/HesaiLidar_General_SDK/src/PandarGeneralRaw/src/pcap_reader.cpp.o CMakeFiles/PandarGeneral.dir/src/HesaiLidar_General_SDK/src/PandarGeneralRaw/src/pandarGeneral.cc.o  -lboost_system -lboost_filesystem -lboost_date_time -lboost_iostreams -lboost_regex /usr/lib/x86_64-linux-gnu/libpcl_io.so /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 -lpcap /usr/lib/x86_64-linux-gnu/libpcl_octree.so /usr/lib/x86_64-linux-gnu/libpcl_common.so /usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.71.0 -lpthread 
