# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/ws_voxel_slam2_vscode/devel/include".split(';') if "/home/<USER>/ws_voxel_slam2_vscode/devel/include" != "" else []
PROJECT_CATKIN_DEPENDS = "message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "hesai_lidar"
PROJECT_SPACE_DIR = "/home/<USER>/ws_voxel_slam2_vscode/devel"
PROJECT_VERSION = "0.2.0"
