/usr/bin/c++ -fPIC   -shared -Wl,-soname,libvoxelslam_pointcloud2.so -o /home/<USER>/ws_voxel_slam2_vscode/devel/lib/libvoxelslam_pointcloud2.so CMakeFiles/voxelslam_pointcloud2.dir/voxelslam_pointcloud2_autogen/mocs_compilation.cpp.o CMakeFiles/voxelslam_pointcloud2.dir/src/voxelslam_pc2.cpp.o   -L/opt/ros/noetic/lib  -Wl,-rpath,/opt/ros/noetic/lib: /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.12.8 /opt/ros/noetic/lib/librviz.so -lOgreOverlay -lOgreMain -lOpenGL -lGLX -lGLU /opt/ros/noetic/lib/libimage_transport.so /opt/ros/noetic/lib/libinteractive_markers.so /opt/ros/noetic/lib/liblaser_geometry.so /opt/ros/noetic/lib/libtf.so /opt/ros/noetic/lib/libresource_retriever.so /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libtf2.so /opt/ros/noetic/lib/liburdf.so -lurdfdom_sensor -lurdfdom_model_state -lurdfdom_model -lurdfdom_world -ltinyxml /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 /opt/ros/noetic/lib/librosconsole_bridge.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.12.8 /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.12.8 
